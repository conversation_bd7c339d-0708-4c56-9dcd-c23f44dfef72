分类	方法名称	功能描述	参数	返回值	使用示例	备注
访客身份识别	getVisitorId()	获取访客的16位字符ID	无	16位十六进制字符串	_paq.push([function() { var visitorId = this.getVisitorId(); }]);	用于唯一标识访客
访客身份识别	setVisitorId(visitorId)	设置访客ID	visitorId: 16位十六进制字符串	无	_paq.push(['setVisitorId', '0123456789abcdef']);	不会持久化，每次页面加载需重新设置
访客身份识别	getVisitorInfo()	获取访客cookie信息数组	无	数组	_paq.push([function() { var info = this.getVisitorInfo(); }]);	包含访客的详细信息
用户身份管理	setUserId(userId)	设置用户ID（如邮箱或用户名）	userId: 字符串	无	_paq.push(['setUserId', '<EMAIL>']);	用于跨设备跟踪同一用户
用户身份管理	getUserId()	获取已设置的用户ID	无	字符串或undefined	_paq.push([function() { var userId = this.getUserId(); }]);	返回之前设置的用户ID
用户身份管理	resetUserId()	清除用户ID	无	无	_paq.push(['resetUserId']);	用户登出时调用
自定义变量（访客级别）	setCustomVariable(index, name, value, 'visit')	设置访客级别自定义变量	index: 1-5, name: 变量名, value: 变量值, scope: 'visit'	无	_paq.push(['setCustomVariable', 1, 'UserType', 'Premium', 'visit']);	在整个访问期间保持
自定义变量（访客级别）	getCustomVariable(index, 'visit')	获取访客级别自定义变量	index: 1-5, scope: 'visit'	数组[name, value]或false	_paq.push([function() { var cv = this.getCustomVariable(1, 'visit'); }]);	获取指定索引的自定义变量
自定义变量（访客级别）	deleteCustomVariable(index, 'visit')	删除访客级别自定义变量	index: 1-5, scope: 'visit'	无	_paq.push(['deleteCustomVariable', 1, 'visit']);	删除指定索引的自定义变量
自定义变量（访客级别）	storeCustomVariablesInCookie()	将访客级别自定义变量存储在cookie中	无	无	_paq.push(['storeCustomVariablesInCookie']);	使getCustomVariable在后续页面中可用
自定义维度（需要插件）	setCustomDimension(id, value)	设置自定义维度	id: 维度ID, value: 维度值	无	_paq.push(['setCustomDimension', 1, 'Member']);	需要Custom Dimensions插件
自定义维度（需要插件）	getCustomDimension(id)	获取自定义维度值	id: 维度ID	字符串或undefined	_paq.push([function() { var dim = this.getCustomDimension(1); }]);	获取指定ID的自定义维度
自定义维度（需要插件）	deleteCustomDimension(id)	删除自定义维度	id: 维度ID	无	_paq.push(['deleteCustomDimension', 1]);	删除指定ID的自定义维度
归因信息	getAttributionInfo()	获取访客归因信息数组	无	数组	_paq.push([function() { var attr = this.getAttributionInfo(); }]);	包含推荐来源信息和活动信息
归因信息	getAttributionCampaignName()	获取活动名称	无	字符串	_paq.push([function() { var campaign = this.getAttributionCampaignName(); }]);	获取归因的活动名称
归因信息	getAttributionCampaignKeyword()	获取活动关键词	无	字符串	_paq.push([function() { var keyword = this.getAttributionCampaignKeyword(); }]);	获取归因的活动关键词
归因信息	getAttributionReferrerTimestamp()	获取推荐来源时间戳	无	时间戳	_paq.push([function() { var timestamp = this.getAttributionReferrerTimestamp(); }]);	获取推荐来源的时间戳
归因信息	getAttributionReferrerUrl()	获取推荐来源URL	无	字符串	_paq.push([function() { var refUrl = this.getAttributionReferrerUrl(); }]);	获取推荐来源的URL
浏览器检测	disableBrowserFeatureDetection()	禁用浏览器特征检测	无	无	_paq.push(['disableBrowserFeatureDetection']);	不收集浏览器分辨率、插件等信息
浏览器检测	enableBrowserFeatureDetection()	启用浏览器特征检测	无	无	_paq.push(['enableBrowserFeatureDetection']);	重新启用浏览器特征检测
页面视图ID	getPageViewId()	获取页面视图ID	无	字符串或undefined	_paq.push([function() { var pvId = this.getPageViewId(); }]);	获取当前页面视图的唯一ID
页面视图ID	setPageViewId(pageViewId)	设置页面视图ID	pageViewId: 字符串	无	_paq.push(['setPageViewId', 'custom-page-view-id']);	为每次页面视图设置自定义ID
URL和推荐来源	getCurrentUrl()	获取当前页面URL	无	字符串	_paq.push([function() { var url = this.getCurrentUrl(); }]);	返回当前页面的URL
URL和推荐来源	setCustomUrl(url)	设置自定义页面URL	url: 字符串	无	_paq.push(['setCustomUrl', 'https://example.com/custom-page']);	覆盖页面报告的URL
URL和推荐来源	setReferrerUrl(url)	设置推荐来源URL	url: 字符串	无	_paq.push(['setReferrerUrl', 'https://referrer.com']);	覆盖检测到的HTTP推荐来源
活动跟踪	setCampaignNameKey(name)	设置活动名称参数	name: 字符串或数组	无	_paq.push(['setCampaignNameKey', 'campaign_name']);	自定义活动名称参数名
活动跟踪	setCampaignKeywordKey(keyword)	设置活动关键词参数	keyword: 字符串或数组	无	_paq.push(['setCampaignKeywordKey', 'campaign_keyword']);	自定义活动关键词参数名

数据结构说明						
getVisitorInfo()返回数组结构						
索引	字段名称	描述	示例值			
0	访客ID	16位十六进制字符串	0123456789abcdef			
1	首次访问时间戳	Unix时间戳	1640995200			
2	上次访问时间戳	Unix时间戳	1640995200			
3	当前访问时间戳	Unix时间戳	1640995200			
4	访问次数	数字	5			
5	自定义变量	JSON格式字符串	{"1":["UserType","Premium"]}			

getAttributionInfo()返回数组结构						
索引	字段名称	描述	示例值			
0	推荐来源名称	字符串	Google			
1	推荐来源关键词	字符串	matomo analytics			
2	推荐来源时间戳	Unix时间戳	1640995200			
3	推荐来源URL	完整URL	https://www.google.com/search?q=matomo			

使用场景示例						
场景	代码示例	说明	备注			
获取完整访客信息	_paq.push([function() { var visitorId = this.getVisitorId(); var visitorInfo = this.getVisitorInfo(); console.log('访客ID:', visitorId); }]);	获取访客的基本信息	适用于用户行为分析			
设置访客分类	_paq.push(['setCustomVariable', 1, 'UserType', 'Premium', 'visit']); _paq.push(['setCustomVariable', 2, 'Segment', 'B2B', 'visit']);	为访客设置分类标签	用于细分用户群体			
跨设备用户跟踪	if (userLoggedIn) { _paq.push(['setUserId', userEmail]); }	登录用户的跨设备跟踪	需要用户登录状态			
获取归因信息	_paq.push([function() { var attribution = this.getAttributionInfo(); console.log('来源:', attribution[0]); }]);	分析用户来源渠道	用于营销效果分析			

注意事项						
项目	说明	重要性	建议			
自定义变量限制	每个作用域最多5个自定义变量	高	合理规划变量使用			
用户ID格式	建议使用邮箱或唯一用户名	中	确保跨设备一致性			
浏览器兼容性	支持IE8及以上版本	中	考虑目标用户群体			
隐私合规	某些方法需要用户同意	高	遵守GDPR等法规			
性能影响	频繁调用可能影响页面性能	中	适度使用跟踪方法			
